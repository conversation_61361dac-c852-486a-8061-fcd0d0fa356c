"use client"

import { useState, useEffect, use<PERSON>allback } from "react"
import { Search, X, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { SearchSuggestions } from "@/components/search-suggestions"
import { Toolbox, type Tool, type Category } from "@/components/toolbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ToolCard } from "@/components/tool-card"
import { EnhancedDataManagement } from "@/components/enhanced-data-management"
// Import AlertDialog components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialog<PERSON>eader,
  AlertD<PERSON>ogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"
import { useCustomToast } from "@/hooks/use-custom-toast"
import { hybridStorage } from "@/lib/hybrid-storage"

// 保留作为默认数据，但主要从 Supabase 加载
const TOOLS_STORAGE_KEY = "toolmaster-tools"
const CATEGORIES_STORAGE_KEY = "toolmaster-categories"

// Full 9-category structure
const defaultCategories: Category[] = [
  {
    id: "development",
    name: "开发工具",
    subcategories: [
      {
        id: "code-editor",
        name: "代码编辑",
        subsubcategories: [
          { id: "online-editor", name: "在线编辑器" },
          { id: "ide", name: "集成开发环境" },
          { id: "text-editor", name: "文本编辑器" },
          { id: "code-formatter", name: "代码格式化" },
        ],
      },
      {
        id: "version-control",
        name: "版本控制",
        subsubcategories: [
          { id: "git-tools", name: "Git工具" },
          { id: "collaboration", name: "协作平台" },
          { id: "code-review", name: "代码审查" },
          { id: "repository", name: "代码仓库" },
        ],
      },
      {
        id: "api-tools",
        name: "API工具",
        subsubcategories: [
          { id: "api-testing", name: "API测试" },
          { id: "api-docs", name: "API文档" },
          { id: "mock-tools", name: "Mock工具" },
          { id: "api-gateway", name: "API网关" },
        ],
      },
      {
        id: "database",
        name: "数据库工具",
        subsubcategories: [
          { id: "db-client", name: "数据库客户端" },
          { id: "db-design", name: "数据库设计" },
          { id: "db-migration", name: "数据迁移" },
          { id: "db-backup", name: "数据备份" },
        ],
      },
      {
        id: "deployment",
        name: "部署运维",
        subsubcategories: [
          { id: "ci-cd", name: "持续集成" },
          { id: "monitoring", name: "监控工具" },
          { id: "container", name: "容器化" },
          { id: "cloud-platform", name: "云平台" },
        ],
      },
      {
        id: "debugging",
        name: "调试测试",
        subsubcategories: [
          { id: "debugger", name: "调试器" },
          { id: "unit-testing", name: "单元测试" },
          { id: "integration-testing", name: "集成测试" },
          { id: "performance-testing", name: "性能测试" },
        ],
      },
    ],
  },
  {
    id: "design",
    name: "设计工具",
    subcategories: [
      {
        id: "ui-design",
        name: "UI设计",
        subsubcategories: [
          { id: "prototyping", name: "原型设计" },
          { id: "wireframe", name: "线框图" },
          { id: "design-system", name: "设计系统" },
          { id: "user-testing", name: "用户测试" },
        ],
      },
      {
        id: "graphics",
        name: "图形设计",
        subsubcategories: [
          { id: "vector-graphics", name: "矢量图形" },
          { id: "photo-editing", name: "图片编辑" },
          { id: "illustration", name: "插画设计" },
          { id: "logo-design", name: "标志设计" },
        ],
      },
      {
        id: "color-tools",
        name: "色彩工具",
        subsubcategories: [
          { id: "color-picker", name: "取色器" },
          { id: "color-palette", name: "调色板" },
          { id: "gradient", name: "渐变工具" },
          { id: "color-theory", name: "色彩理论" },
        ],
      },
      {
        id: "icon-fonts",
        name: "图标字体",
        subsubcategories: [
          { id: "icon-library", name: "图标库" },
          { id: "font-tools", name: "字体工具" },
          { id: "emoji", name: "表情符号" },
          { id: "typography", name: "字体排版" },
        ],
      },
      {
        id: "3d-design",
        name: "3D设计",
        subsubcategories: [
          { id: "3d-modeling", name: "3D建模" },
          { id: "3d-rendering", name: "3D渲染" },
          { id: "3d-animation", name: "3D动画" },
          { id: "3d-printing", name: "3D打印" },
        ],
      },
      {
        id: "multimedia",
        name: "多媒体设计",
        subsubcategories: [
          { id: "video-editing", name: "视频编辑" },
          { id: "audio-editing", name: "音频编辑" },
          { id: "motion-graphics", name: "动态图形" },
          { id: "presentation", name: "演示设计" },
        ],
      },
    ],
  },
  {
    id: "productivity",
    name: "效率工具",
    subcategories: [
      {
        id: "note-taking",
        name: "笔记工具",
        subsubcategories: [
          { id: "markdown", name: "Markdown编辑" },
          { id: "knowledge-base", name: "知识库" },
          { id: "mind-map", name: "思维导图" },
          { id: "note-sync", name: "笔记同步" },
        ],
      },
      {
        id: "task-management",
        name: "任务管理",
        subsubcategories: [
          { id: "todo-list", name: "待办清单" },
          { id: "project-management", name: "项目管理" },
          { id: "time-tracking", name: "时间追踪" },
          { id: "team-collaboration", name: "团队协作" },
        ],
      },
      {
        id: "automation",
        name: "自动化工具",
        subsubcategories: [
          { id: "workflow", name: "工作流" },
          { id: "scripting", name: "脚本工具" },
          { id: "integration", name: "集成平台" },
          { id: "scheduling", name: "任务调度" },
        ],
      },
      {
        id: "office-tools",
        name: "办公工具",
        subsubcategories: [
          { id: "document", name: "文档处理" },
          { id: "spreadsheet", name: "表格工具" },
          { id: "presentation", name: "演示文稿" },
          { id: "pdf-tools", name: "PDF工具" },
        ],
      },
    ],
  },
  {
    id: "learning",
    name: "学习资源",
    subcategories: [
      {
        id: "programming",
        name: "编程学习",
        subsubcategories: [
          { id: "coding-practice", name: "编程练习" },
          { id: "algorithm", name: "算法学习" },
          { id: "tutorial", name: "教程平台" },
          { id: "code-challenge", name: "编程挑战" },
        ],
      },
      {
        id: "language",
        name: "语言学习",
        subsubcategories: [
          { id: "vocabulary", name: "词汇学习" },
          { id: "grammar", name: "语法练习" },
          { id: "pronunciation", name: "发音练习" },
          { id: "conversation", name: "口语对话" },
        ],
      },
      {
        id: "reference",
        name: "参考资料",
        subsubcategories: [
          { id: "documentation", name: "技术文档" },
          { id: "cheatsheet", name: "速查表" },
          { id: "examples", name: "代码示例" },
          { id: "wiki", name: "知识百科" },
        ],
      },
      {
        id: "online-courses",
        name: "在线课程",
        subsubcategories: [
          { id: "mooc", name: "慕课平台" },
          { id: "video-courses", name: "视频课程" },
          { id: "certification", name: "认证考试" },
          { id: "live-courses", name: "直播课程" },
        ],
      },
    ],
  },
  {
    id: "entertainment",
    name: "娱乐工具",
    subcategories: [
      {
        id: "media-streaming",
        name: "媒体播放",
        subsubcategories: [
          { id: "video-streaming", name: "视频播放" },
          { id: "music-streaming", name: "音乐播放" },
          { id: "podcast", name: "播客平台" },
          { id: "live-streaming", name: "直播平台" },
        ],
      },
      {
        id: "games",
        name: "游戏娱乐",
        subsubcategories: [
          { id: "online-games", name: "在线游戏" },
          { id: "game-tools", name: "游戏工具" },
          { id: "game-community", name: "游戏社区" },
          { id: "mobile-games", name: "手机游戏" },
        ],
      },
      {
        id: "content-download",
        name: "内容下载",
        subsubcategories: [
          { id: "video-download", name: "视频下载" },
          { id: "music-download", name: "音乐下载" },
          { id: "movie-download", name: "电影下载" },
          { id: "ebook-download", name: "电子书下载" },
        ],
      },
      {
        id: "social-entertainment",
        name: "社交娱乐",
        subsubcategories: [
          { id: "social-media", name: "社交媒体" },
          { id: "chat-tools", name: "聊天工具" },
          { id: "community", name: "社区论坛" },
          { id: "dating-apps", name: "交友应用" },
        ],
      },
    ],
  },
  {
    id: "life-service",
    name: "生活服务",
    subcategories: [
      {
        id: "daily-tools",
        name: "日常工具",
        subsubcategories: [
          { id: "weather", name: "天气查询" },
          { id: "calendar", name: "日历工具" },
          { id: "reminder", name: "提醒工具" },
          { id: "alarm-clock", name: "闹钟计时" },
        ],
      },
      {
        id: "travel",
        name: "旅行出行",
        subsubcategories: [
          { id: "map-navigation", name: "地图导航" },
          { id: "booking", name: "预订服务" },
          { id: "travel-guide", name: "旅行指南" },
          { id: "transportation", name: "交通工具" },
        ],
      },
      {
        id: "health-fitness",
        name: "健康健身",
        subsubcategories: [
          { id: "fitness-tracker", name: "健身追踪" },
          { id: "health-monitor", name: "健康监测" },
          { id: "nutrition", name: "营养管理" },
          { id: "medical-info", name: "医疗信息" },
        ],
      },
      {
        id: "finance",
        name: "金融理财",
        subsubcategories: [
          { id: "budget-tracker", name: "预算追踪" },
          { id: "investment", name: "投资理财" },
          { id: "currency", name: "汇率查询" },
          { id: "banking", name: "银行服务" },
        ],
      },
    ],
  },
  {
    id: "business",
    name: "商业工具",
    subcategories: [
      {
        id: "marketing",
        name: "营销推广",
        subsubcategories: [
          { id: "seo-tools", name: "SEO工具" },
          { id: "social-marketing", name: "社交营销" },
          { id: "email-marketing", name: "邮件营销" },
          { id: "content-marketing", name: "内容营销" },
        ],
      },
      {
        id: "analytics",
        name: "数据分析",
        subsubcategories: [
          { id: "web-analytics", name: "网站分析" },
          { id: "business-intelligence", name: "商业智能" },
          { id: "data-visualization", name: "数据可视化" },
          { id: "market-research", name: "市场调研" },
        ],
      },
      {
        id: "customer-service",
        name: "客户服务",
        subsubcategories: [
          { id: "live-chat", name: "在线客服" },
          { id: "help-desk", name: "帮助台" },
          { id: "feedback", name: "反馈收集" },
          { id: "crm-system", name: "客户管理" },
        ],
      },
      {
        id: "e-commerce",
        name: "电子商务",
        subsubcategories: [
          { id: "online-store", name: "在线商店" },
          { id: "payment", name: "支付工具" },
          { id: "inventory", name: "库存管理" },
          { id: "logistics", name: "物流管理" },
        ],
      },
    ],
  },
  {
    id: "system",
    name: "系统工具",
    subcategories: [
      {
        id: "network-tools",
        name: "网络工具",
        subsubcategories: [
          { id: "speed-test", name: "网速测试" },
          { id: "dns-tools", name: "DNS工具" },
          { id: "proxy-vpn", name: "代理VPN" },
          { id: "network-monitor", name: "网络监控" },
        ],
      },
      {
        id: "security",
        name: "安全工具",
        subsubcategories: [
          { id: "password-manager", name: "密码管理" },
          { id: "encryption", name: "加密工具" },
          { id: "security-scan", name: "安全扫描" },
          { id: "antivirus", name: "杀毒软件" },
        ],
      },
      {
        id: "file-tools",
        name: "文件工具",
        subsubcategories: [
          { id: "file-converter", name: "文件转换" },
          { id: "file-compress", name: "文件压缩" },
          { id: "file-recovery", name: "文件恢复" },
          { id: "file-sync", name: "文件同步" },
        ],
      },
      {
        id: "system-monitor",
        name: "系统监控",
        subsubcategories: [
          { id: "performance", name: "性能监控" },
          { id: "resource-usage", name: "资源使用" },
          { id: "system-info", name: "系统信息" },
          { id: "disk-cleanup", name: "磁盘清理" },
        ],
      },
    ],
  },
  {
    id: "ai-tools",
    name: "AI工具",
    subcategories: [
      {
        id: "text-generation",
        name: "文本生成",
        subsubcategories: [
          { id: "chatbot", name: "聊天机器人" },
          { id: "writing-assistant", name: "写作助手" },
          { id: "content-creation", name: "内容创作" },
          { id: "translation", name: "智能翻译" },
        ],
      },
      {
        id: "image-generation",
        name: "图像生成",
        subsubcategories: [
          { id: "ai-art", name: "AI绘画" },
          { id: "photo-enhancement", name: "图片增强" },
          { id: "image-editing", name: "智能编辑" },
          { id: "avatar-generator", name: "头像生成" },
        ],
      },
      {
        id: "code-assistant",
        name: "代码助手",
        subsubcategories: [
          { id: "code-completion", name: "代码补全" },
          { id: "code-review", name: "代码审查" },
          { id: "bug-detection", name: "错误检测" },
          { id: "code-generation", name: "代码生成" },
        ],
      },
      {
        id: "data-analysis",
        name: "数据分析",
        subsubcategories: [
          { id: "data-mining", name: "数据挖掘" },
          { id: "predictive-analysis", name: "预测分析" },
          { id: "pattern-recognition", name: "模式识别" },
          { id: "ai-research", name: "AI研究" },
        ],
      },
    ],
  },
  {
    id: "other",
    name: "其他工具",
    subcategories: [
      {
        id: "utility",
        name: "实用工具",
        subsubcategories: [
          { id: "calculator", name: "计算器" },
          { id: "unit-converter", name: "单位转换" },
          { id: "qr-generator", name: "二维码生成" },
          { id: "random-generator", name: "随机生成" },
        ],
      },
      {
        id: "generator",
        name: "生成工具",
        subsubcategories: [
          { id: "text-generator", name: "文本生成" },
          { id: "image-generator", name: "图片生成" },
          { id: "data-generator", name: "数据生成" },
          { id: "password-generator", name: "密码生成" },
        ],
      },
      {
        id: "testing",
        name: "测试工具",
        subsubcategories: [
          { id: "website-test", name: "网站测试" },
          { id: "performance-test", name: "性能测试" },
          { id: "compatibility-test", name: "兼容性测试" },
          { id: "load-test", name: "负载测试" },
        ],
      },
      {
        id: "converter",
        name: "转换工具",
        subsubcategories: [
          { id: "format-converter", name: "格式转换" },
          { id: "encoding-converter", name: "编码转换" },
          { id: "time-converter", name: "时间转换" },
          { id: "currency-converter", name: "货币转换" },
        ],
      },
    ],
  },
]

// 检查分类是否需要更新的函数
function checkIfCategoriesNeedUpdate(currentCategories: Category[], defaultCategories: Category[]): boolean {
  // 检查一级分类数量
  if (currentCategories.length !== defaultCategories.length) {
    return true
  }

  // 检查每个一级分类的二级分类数量
  for (let i = 0; i < currentCategories.length; i++) {
    const current = currentCategories[i]
    const defaultCat = defaultCategories.find(cat => cat.id === current.id)

    if (!defaultCat) {
      return true
    }

    if (current.subcategories.length !== defaultCat.subcategories.length) {
      return true
    }

    // 检查三级分类数量
    for (let j = 0; j < current.subcategories.length; j++) {
      const currentSub = current.subcategories[j]
      const defaultSub = defaultCat.subcategories.find(sub => sub.id === currentSub.id)

      if (!defaultSub) {
        return true
      }

      if (currentSub.subsubcategories.length !== defaultSub.subsubcategories.length) {
        return true
      }
    }
  }

  return false
}

export default function ToolShiftApp() {
  const { toast } = useToast()
  const customToast = useCustomToast()
  // Add pagination related states
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12) // Number of items per page
  const [viewMode, setViewMode] = useState<"pagination" | "infinite">("pagination")
  const [loadedItems, setLoadedItems] = useState(12) // Number of items loaded for infinite scroll

  const [tools, setTools] = useState<Tool[]>([])
  const [categories, setCategories] = useState<Category[]>(defaultCategories)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>("")
  const [selectedSubsubcategory, setSelectedSubsubcategory] = useState<string>("")
  const [selectedTag, setSelectedTag] = useState<string>("") // New: selected tag
  const [showSensitive, setShowSensitive] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])

  // 同步状态
  const [isInitialized, setIsInitialized] = useState(false)
  const [syncStatus, setSyncStatus] = useState({ isOnline: true, syncing: false })

  // Data management dialog state
  const [dataManagementDialog, setDataManagementDialog] = useState(false) // Keep this for the DataManagement component

  const [toolToDelete, setToolToDelete] = useState<Tool | null>(null) // Tool to be deleted
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false) // Delete confirmation dialog
  const [showToolDetailsDialog, setShowToolDetailsDialog] = useState(false) // View description dialog
  const [currentToolDetails, setCurrentToolDetails] = useState<Tool | null>(null) // Current tool for viewing description

  const [showExportCategoryDialog, setShowExportCategoryDialog] = useState(false) // Export category data dialog
  const [categoryToExport, setCategoryToExport] = useState<{
    categoryId?: string
    subcategoryId?: string
    subsubcategoryId?: string
    name: string
  } | null>(null) // Category info to be exported

  // 深度搜索相关状态
  const [isDeepSearching, setIsDeepSearching] = useState(false)
  const [deepSearchResults, setDeepSearchResults] = useState<{
    searchSummary: string
    recommendedTools: Array<{
      id: string
      relevanceScore: number
      recommendReason: string
    }>
    searchInsights: string
  } | null>(null)
  const [showDeepSearchResults, setShowDeepSearchResults] = useState(false)

  // 全网搜索相关状态
  const [isGlobalSearching, setIsGlobalSearching] = useState(false)
  const [globalSearchResults, setGlobalSearchResults] = useState<{
    searchSummary: string
    recommendedTools: Array<Tool & {
      relevanceScore: number
      recommendReason: string
      isGlobalSearch: boolean
    }>
    searchInsights: string
  } | null>(null)
  const [showGlobalSearchResults, setShowGlobalSearchResults] = useState(false)
  const [globalSearchCache, setGlobalSearchCache] = useState<Tool[]>([])

  // 初始化搜索历史
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem("toolmaster-search-history")
      if (savedHistory) {
        setSearchHistory(JSON.parse(savedHistory))
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  }, [])

  // 初始化混合存储
  useEffect(() => {
    const initializeStorage = async () => {
      try {
        await hybridStorage.initialize({
          onToolsChange: (tools) => {
            setTools(tools)
          },
          onCategoriesChange: (categories) => {
            setCategories(categories)
          },
          onSyncStatusChange: (syncing) => {
            setSyncStatus(prev => ({ ...prev, syncing }))
          }
        })

        // 加载初始数据
        const initialTools = hybridStorage.getTools()
        const initialCategories = hybridStorage.getCategories()

        setTools(initialTools)

        // 检查是否需要更新分类数据
        if (initialCategories.length === 0) {
          // 如果没有分类数据，使用默认分类并保存到数据库
          console.log('没有找到分类数据，使用默认分类并保存到数据库')
          setCategories(defaultCategories)
          await hybridStorage.updateCategories(defaultCategories)
        } else {
          // 检查分类结构是否需要更新
          const needsUpdate = checkIfCategoriesNeedUpdate(initialCategories, defaultCategories)
          if (needsUpdate) {
            console.log('检测到分类结构更新，正在更新数据库...')
            setCategories(defaultCategories)
            await hybridStorage.updateCategories(defaultCategories)
            toast({
              title: "分类结构已更新",
              description: "已应用最新的分类结构",
            })
          } else {
            setCategories(initialCategories)
          }
        }

        setIsInitialized(true)
        console.log('混合存储初始化完成')
      } catch (error) {
        console.error('混合存储初始化失败:', error)

        // 降级到本地存储
        try {
          const storedTools = localStorage.getItem(TOOLS_STORAGE_KEY)
          const storedCategories = localStorage.getItem(CATEGORIES_STORAGE_KEY)

          if (storedTools) {
            setTools(JSON.parse(storedTools))
          } else {
            // 如果没有本地数据，使用示例数据
            const sampleTools: Tool[] = [
              {
                id: "1",
                name: "GitHub",
                url: "https://github.com",
                description: "全球最大的代码托管平台，支持Git版本控制和协作开发",
                tags: ["代码", "版本控制", "开源", "协作"],
                category: "development",
                subcategory: "version-control",
                subsubcategory: "git-tools",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
              {
                id: "2",
                name: "Figma",
                url: "https://figma.com",
                description: "现代化的界面设计工具，支持实时协作和原型设计",
                tags: ["设计", "UI", "原型", "协作"],
                category: "design",
                subcategory: "ui-design",
                subsubcategory: "prototyping",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
              {
                id: "3",
                name: "Notion",
                url: "https://notion.so",
                description: "全能的笔记和知识管理工具，支持数据库、看板等多种视图",
                tags: ["笔记", "知识管理", "协作", "数据库"],
                category: "productivity",
                subcategory: "note-taking",
                subsubcategory: "knowledge-base",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
              {
                id: "4",
                name: "VS Code",
                url: "https://code.visualstudio.com",
                description: "微软开发的免费代码编辑器，支持丰富的插件生态",
                tags: ["编辑器", "IDE", "开发", "插件"],
                category: "development",
                subcategory: "code-editor",
                subsubcategory: "ide",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
              {
                id: "5",
                name: "ChatGPT",
                url: "https://chat.openai.com",
                description: "OpenAI开发的AI对话助手，可以回答问题、写代码、创作内容",
                tags: ["AI", "对话", "助手", "创作"],
                category: "ai-tools",
                subcategory: "text-generation",
                subsubcategory: "chatbot",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
              {
                id: "6",
                name: "Google Translate",
                url: "https://translate.google.com/",
                description: "Google提供的免费在线翻译服务",
                tags: ["翻译", "语言", "工具"],
                category: "ai-tools",
                subcategory: "text-generation",
                subsubcategory: "writing-assistant",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
              {
                id: "7",
                name: "Canva",
                url: "https://www.canva.com/",
                description: "在线图形设计工具，提供丰富的模板和素材",
                tags: ["设计", "图形", "模板", "在线"],
                category: "design",
                subcategory: "ui-design",
                subsubcategory: "graphics",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
              {
                id: "8",
                name: "Todoist",
                url: "https://todoist.com/",
                description: "流行的任务管理和待办事项清单应用",
                tags: ["任务", "待办", "效率", "管理"],
                category: "productivity",
                subcategory: "task-management",
                subsubcategory: "todo-list",
                addedAt: new Date().toISOString(),
                sensitive: false,
              },
            ]
            setTools(sampleTools)
          }

          if (storedCategories) {
            setCategories(JSON.parse(storedCategories))
          } else {
            setCategories(defaultCategories)
          }
        } catch (fallbackError) {
          console.error('降级到本地存储也失败:', fallbackError)
          setCategories(defaultCategories)
        }

        setIsInitialized(true)

        customToast.info("离线模式", "无法连接到云端，当前使用离线模式。")
      }
    }

    initializeStorage()
  }, [toast])

  // 工具操作函数 - 使用混合存储
  const handleAddTool = async (tool: Tool) => {
    if (!isInitialized) return

    try {
      const newTool = await hybridStorage.addTool(tool)
      if (!newTool) {
        customToast.error("添加失败", "添加工具时发生错误，请重试。")
      }
    } catch (error) {
      console.error('添加工具失败:', error)
      customToast.error("添加失败", "添加工具时发生错误，请重试。")
    }
  }

  const handleUpdateTool = (updatedTool: Tool) => {
    // 暂时保留本地更新逻辑，后续可以扩展为支持编辑功能
    setTools((prev) => prev.map((tool) => (tool.id === updatedTool.id ? updatedTool : tool)))
  }

  const handleDeleteTool = async (id: string) => {
    if (!isInitialized) return

    try {
      const success = await hybridStorage.deleteTool(id)
      if (!success) {
        toast({
          title: "删除失败",
          description: "删除工具时发生错误，请重试。",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('删除工具失败:', error)
      toast({
        title: "删除失败",
        description: "删除工具时发生错误，请重试。",
        variant: "destructive",
      })
    }
  }

  const handleUpdateCategories = async (updatedCategories: Category[]) => {
    if (!isInitialized) return

    try {
      const success = await hybridStorage.updateCategories(updatedCategories)
      if (!success) {
        toast({
          title: "更新失败",
          description: "更新分类时发生错误，请重试。",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('更新分类失败:', error)
      toast({
        title: "更新失败",
        description: "更新分类时发生错误，请重试。",
        variant: "destructive",
      })
    }
  }

  // 强制更新分类到最新结构
  const forceUpdateCategories = async () => {
    if (!isInitialized) return

    try {
      console.log('强制更新分类结构到最新版本...')
      const success = await hybridStorage.updateCategories(defaultCategories)
      if (!success) {
        throw new Error('强制更新分类失败')
      }

      setCategories(defaultCategories)
      toast({
        title: "分类结构已强制更新",
        description: "已应用最新的分类结构",
      })
    } catch (error) {
      console.error('强制更新分类失败:', error)
      toast({
        title: "强制更新失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    }
  }

  // 在开发环境下，可以通过控制台调用这个函数
  if (typeof window !== 'undefined') {
    (window as any).forceUpdateCategories = forceUpdateCategories
  }

  const handleImportData = async (data: { tools: Tool[]; categories: Category[] }) => {
    if (!isInitialized) return

    try {
      // 更新分类
      await hybridStorage.updateCategories(data.categories)

      // 添加工具（注意：这会添加到现有工具中，而不是替换）
      for (const tool of data.tools) {
        await hybridStorage.addTool(tool)
      }

      customToast.success("导入成功", `成功导入 ${data.tools.length} 个工具和分类数据。`)
    } catch (error) {
      console.error('导入数据失败:', error)
      customToast.error("导入失败", "导入数据时发生错误，请重试。")
    }
  }

  const handleExportData = () => {
    return { tools, categories }
  }

  // AI analysis tool information (enhanced version)
  const analyzeToolWithAI = async (url: string, name: string, description: string) => {
    // This function is not directly used in page.tsx anymore, but kept for context if needed elsewhere.
    // It's primarily used within Toolbox's internal add tool logic.
    // Simulate AI API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Smart classification logic
    const classifyTool = (url: string, name: string, description: string) => {
      const text = `${name} ${description} ${url}`.toLowerCase()

      // Entertainment tools - highest priority
      if (
        text.includes("电影") ||
        text.includes("movie") ||
        text.includes("4k") ||
        text.includes("下载") ||
        text.includes("download") ||
        text.includes("torrent") ||
        text.includes("bt") ||
        text.includes("磁力")
      ) {
        if (text.includes("电影") || text.includes("movie") || text.includes("4k")) {
          return { category: "entertainment", subcategory: "content-download", subsubcategory: "movie-download" }
        } else if (text.includes("音乐") || text.includes("music")) {
          return { category: "entertainment", subcategory: "content-download", subsubcategory: "music-download" }
        } else if (text.includes("视频") || text.includes("video")) {
          return { category: "entertainment", subcategory: "content-download", subsubcategory: "video-download" }
        }
        return { category: "entertainment", subcategory: "content-download", subsubcategory: "movie-download" }
      }

      // Development tools
      if (
        text.includes("code") ||
        text.includes("github") ||
        text.includes("git") ||
        text.includes("api") ||
        text.includes("开发") ||
        text.includes("编程")
      ) {
        if (text.includes("editor") || text.includes("ide") || text.includes("编辑器")) {
          return { category: "development", subcategory: "code-editor", subsubcategory: "online-editor" }
        } else if (text.includes("git") || text.includes("版本")) {
          return { category: "development", subcategory: "version-control", subsubcategory: "git-tools" }
        } else if (text.includes("api") || text.includes("postman") || text.includes("swagger")) {
          return { category: "development", subcategory: "api-tools", subsubcategory: "api-testing" }
        } else if (text.includes("database") || text.includes("数据库") || text.includes("sql")) {
          return { category: "development", subcategory: "database", subsubcategory: "db-client" }
        } else if (text.includes("deploy") || text.includes("部署") || text.includes("ci") || text.includes("cd")) {
          return { category: "development", subcategory: "deployment", subsubcategory: "ci-cd" }
        }
        return { category: "development", subcategory: "code-editor", subsubcategory: "online-editor" }
      }

      // Design tools
      if (
        text.includes("design") ||
        text.includes("figma") ||
        text.includes("photoshop") ||
        text.includes("设计") ||
        text.includes("图形")
      ) {
        if (text.includes("ui") || text.includes("prototype") || text.includes("原型")) {
          return { category: "design", subcategory: "ui-design", subsubcategory: "prototyping" }
        } else if (text.includes("color") || text.includes("颜色") || text.includes("调色")) {
          return { category: "design", subcategory: "color-tools", subsubcategory: "color-picker" }
        } else if (text.includes("icon") || text.includes("图标") || text.includes("font") || text.includes("字体")) {
          return { category: "design", subcategory: "icon-fonts", subsubcategory: "icon-library" }
        } else if (text.includes("3d") || text.includes("三维") || text.includes("建模")) {
          return { category: "design", subcategory: "3d-design", subsubcategory: "3d-modeling" }
        }
        return { category: "design", subcategory: "graphics", subsubcategory: "vector-graphics" }
      }

      // Learning resources
      if (
        text.includes("learn") ||
        text.includes("tutorial") ||
        text.includes("course") ||
        text.includes("学习") ||
        text.includes("教程") ||
        text.includes("文档")
      ) {
        if (text.includes("programming") || text.includes("coding") || text.includes("编程") || text.includes("算法")) {
          return { category: "learning", subcategory: "programming", subsubcategory: "coding-practice" }
        } else if (
          text.includes("language") ||
          text.includes("english") ||
          text.includes("语言") ||
          text.includes("翻译")
        ) {
          return { category: "learning", subcategory: "language", subsubcategory: "vocabulary" }
        } else if (
          text.includes("doc") ||
          text.includes("reference") ||
          text.includes("文档") ||
          text.includes("参考")
        ) {
          return { category: "learning", subcategory: "reference", subsubcategory: "documentation" }
        } else if (text.includes("course") || text.includes("mooc") || text.includes("课程")) {
          return { category: "learning", subcategory: "online-courses", subsubcategory: "mooc" }
        }
        return { category: "learning", subcategory: "programming", subsubcategory: "tutorial" }
      }

      // Life services
      if (
        text.includes("weather") ||
        text.includes("天气") ||
        text.includes("map") ||
        text.includes("地图") ||
        text.includes("健康") ||
        text.includes("health") ||
        text.includes("金融") ||
        text.includes("finance")
      ) {
        if (text.includes("weather") || text.includes("天气")) {
          return { category: "life-service", subcategory: "daily-tools", subsubcategory: "weather" }
        } else if (text.includes("map") || text.includes("地图") || text.includes("导航")) {
          return { category: "life-service", subcategory: "travel", subsubcategory: "map-navigation" }
        } else if (text.includes("health") || text.includes("健康") || text.includes("fitness")) {
          return { category: "life-service", subcategory: "health-fitness", subsubcategory: "health-monitor" }
        } else if (text.includes("finance") || text.includes("金融") || text.includes("理财")) {
          return { category: "life-service", subcategory: "finance", subsubcategory: "budget-tracker" }
        }
        return { category: "life-service", subcategory: "daily-tools", subsubcategory: "calendar" }
      }

      // Business tools
      if (
        text.includes("seo") ||
        text.includes("marketing") ||
        text.includes("analytics") ||
        text.includes("营销") ||
        text.includes("分析") ||
        text.includes("商业")
      ) {
        if (text.includes("seo") || text.includes("搜索优化")) {
          return { category: "business", subcategory: "marketing", subsubcategory: "seo-tools" }
        } else if (text.includes("analytics") || text.includes("分析") || text.includes("统计")) {
          return { category: "business", subcategory: "analytics", subsubcategory: "web-analytics" }
        } else if (text.includes("customer") || text.includes("客服") || text.includes("support")) {
          return { category: "business", subcategory: "customer-service", subsubcategory: "live-chat" }
        } else if (text.includes("ecommerce") || text.includes("电商") || text.includes("商店")) {
          return { category: "business", subcategory: "e-commerce", subsubcategory: "online-store" }
        }
        return { category: "business", subcategory: "marketing", subsubcategory: "social-marketing" }
      }

      // System tools
      if (
        text.includes("network") ||
        text.includes("security") ||
        text.includes("file") ||
        text.includes("网络") ||
        text.includes("安全") ||
        text.includes("文件") ||
        text.includes("系统")
      ) {
        if (text.includes("speed") || text.includes("测速") || text.includes("网速")) {
          return { category: "system", subcategory: "network-tools", subsubcategory: "speed-test" }
        } else if (text.includes("password") || text.includes("密码") || text.includes("security")) {
          return { category: "system", subcategory: "security", subsubcategory: "password-manager" }
        } else if (text.includes("file") || text.includes("文件") || text.includes("convert")) {
          return { category: "system", subcategory: "file-tools", subsubcategory: "file-converter" }
        } else if (text.includes("monitor") || text.includes("监控") || text.includes("performance")) {
          return { category: "system", subcategory: "system-monitor", subsubcategory: "performance" }
        }
        return { category: "system", subcategory: "network-tools", subsubcategory: "dns-tools" }
      }

      // Productivity tools
      if (
        text.includes("note") ||
        text.includes("task") ||
        text.includes("todo") ||
        text.includes("笔记") ||
        text.includes("任务") ||
        text.includes("办公")
      ) {
        if (text.includes("note") || text.includes("笔记") || text.includes("markdown")) {
          return { category: "productivity", subcategory: "note-taking", subsubcategory: "markdown" }
        } else if (text.includes("task") || text.includes("todo") || text.includes("任务")) {
          return { category: "productivity", subcategory: "task-management", subsubcategory: "todo-list" }
        } else if (text.includes("office") || text.includes("办公") || text.includes("document")) {
          return { category: "productivity", subcategory: "office-tools", subsubcategory: "document" }
        } else if (text.includes("automation") || text.includes("自动化") || text.includes("workflow")) {
          return { category: "productivity", subcategory: "automation", subsubcategory: "workflow" }
        }
        return { category: "productivity", subcategory: "note-taking", subsubcategory: "knowledge-base" }
      }

      // Other tools
      if (
        text.includes("calculator") ||
        text.includes("converter") ||
        text.includes("generator") ||
        text.includes("计算") ||
        text.includes("转换") ||
        text.includes("生成")
      ) {
        if (text.includes("qr") || text.includes("二维码")) {
          return { category: "other", subcategory: "utility", subsubcategory: "qr-generator" }
        } else if (text.includes("calculator") || text.includes("计算")) {
          return { category: "other", subcategory: "utility", subsubcategory: "calculator" }
        } else if (text.includes("convert") || text.includes("转换")) {
          return { category: "other", subcategory: "utility", subsubcategory: "unit-converter" }
        } else if (text.includes("generator") || text.includes("生成")) {
          return { category: "other", subcategory: "generator", subsubcategory: "text-generator" }
        } else if (text.includes("test") || text.includes("测试")) {
          return { category: "other", subcategory: "testing", subsubcategory: "website-test" }
        }
        return { category: "other", subcategory: "utility", subsubcategory: "calculator" }
      }

      // Final default category
      return { category: "other", subcategory: "utility", subsubcategory: "calculator" }
    }

    const classification = classifyTool(url, name, description)

    // Generate smart tags - enhanced version
    const generateTags = (url: string, name: string, description: string, category: string) => {
      const text = `${name} ${description}`.toLowerCase()
      const tags: string[] = []

      // Add basic tags based on category
      switch (category) {
        case "entertainment":
          if (text.includes("电影") || text.includes("movie") || text.includes("4k")) {
            tags.push("电影", "下载", "4K")
          } else if (text.includes("音乐") || text.includes("music")) {
            tags.push("音乐", "下载", "播放")
          } else if (text.includes("视频") || text.includes("video")) {
            tags.push("视频", "下载", "播放")
          } else if (text.includes("游戏") || text.includes("game")) {
            tags.push("游戏", "娱乐", "在线")
          } else {
            tags.push("娱乐", "媒体")
          }
          break
        case "development":
          tags.push("开发", "编程")
          if (text.includes("git")) tags.push("版本控制", "Git")
          if (text.includes("api")) tags.push("API", "接口")
          if (text.includes("database")) tags.push("数据库", "SQL")
          break
        case "design":
          tags.push("设计", "创意")
          if (text.includes("ui")) tags.push("UI", "界面")
          if (text.includes("color")) tags.push("颜色", "调色")
          if (text.includes("3d")) tags.push("3D", "建模")
          break
        case "learning":
          tags.push("学习", "教育")
          if (text.includes("programming")) tags.push("编程", "代码")
          if (text.includes("language")) tags.push("语言", "翻译")
          if (text.includes("course")) tags.push("课程", "培训")
          break
        case "life-service":
          tags.push("生活", "服务")
          if (text.includes("weather")) tags.push("天气", "预报")
          if (text.includes("health")) tags.push("健康", "医疗")
          if (text.includes("finance")) tags.push("金融", "理财")
          break
        case "business":
          tags.push("商业", "企业")
          if (text.includes("seo")) tags.push("SEO", "优化")
          if (text.includes("analytics")) tags.push("分析", "统计")
          if (text.includes("marketing")) tags.push("营销", "推广")
          break
        case "system":
          tags.push("系统", "工具")
          if (text.includes("network")) tags.push("网络", "连接")
          if (text.includes("security")) tags.push("安全", "防护")
          if (text.includes("file")) tags.push("文件", "管理")
          break
        case "productivity":
          tags.push("效率", "生产力")
          if (text.includes("note")) tags.push("笔记", "记录")
          if (text.includes("task")) tags.push("任务", "管理")
          if (text.includes("office")) tags.push("办公", "文档")
          break
        default:
          tags.push("工具", "实用")
      }

      // General tags
      if (text.includes("在线") || url.includes("http")) tags.push("在线")
      if (text.includes("免费") || text.includes("free")) tags.push("免费")
      if (text.includes("开源") || text.includes("open source")) tags.push("开源")
      if (text.includes("ai") || text.includes("人工智能")) tags.push("AI")
      if (text.includes("协作") || text.includes("团队") || text.includes("collaboration")) tags.push("协作")
      if (text.includes("移动") || text.includes("mobile") || text.includes("app")) tags.push("移动端")

      // Deduplicate and limit quantity
      return [...new Set(tags)].slice(0, 6)
    }

    const aiResult = {
      enhancedDescription: description || `${name} 是一个实用的在线工具，提供便捷的功能服务。`,
      tags: generateTags(url, name, description, classification.category),
      category: classification.category,
      subcategory: classification.subcategory,
      subsubcategory: classification.subsubcategory,
      sensitive: false,
    }

    return aiResult
  }

  // Handle exporting category data
  const handleExportCategoryData = (
    categoryId?: string,
    subcategoryId?: string,
    subsubcategoryId?: string,
    name: string,
  ) => {
    setCategoryToExport({ categoryId, subcategoryId, subsubcategoryId, name })
    setShowExportCategoryDialog(true)
  }

  const confirmExportCategoryData = () => {
    if (!categoryToExport) return

    let toolsToExport: Tool[] = []
    let exportName = categoryToExport.name

    if (categoryToExport.subsubcategoryId) {
      toolsToExport = tools.filter(
        (tool) =>
          tool.category === categoryToExport.categoryId &&
          tool.subcategory === categoryToExport.subcategoryId &&
          tool.subsubcategory === categoryToExport.subsubcategoryId,
      )
    } else if (categoryToExport.subcategoryId) {
      toolsToExport = tools.filter(
        (tool) => tool.category === categoryToExport.categoryId && tool.subcategory === categoryToExport.subcategoryId,
      )
    } else if (categoryToExport.categoryId) {
      toolsToExport = tools.filter((tool) => tool.category === categoryToExport.categoryId)
    } else {
      // Export all tools if no specific category is selected (shouldn't happen with right-click context)
      toolsToExport = tools
      exportName = "全部工具"
    }

    try {
      const exportData = {
        type: "ToolShift_Category_Export",
        version: "1.0",
        exportDate: new Date().toISOString(),
        categoryPath: exportName,
        metadata: {
          totalTools: toolsToExport.length,
          exportedBy: "ToolShift",
        },
        tools: toolsToExport,
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `toolshift-category-${exportName.replace(/\s/g, "-")}-${new Date().toISOString().split("T")[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      // Log export
      const exportLog = {
        action: "EXPORT_CATEGORY_DATA",
        category: exportName,
        toolsCount: toolsToExport.length,
        timestamp: new Date().toISOString(),
      }
      setTimeout(() => {
        const logs = JSON.parse(localStorage.getItem("toolshift-logs") || "[]")
        logs.push(exportLog)
        localStorage.setItem("toolshift-logs", JSON.stringify(logs.slice(-100)))
      }, 0)

      // Removed setMessage as it's not used in page.tsx for this dialog
      toast({
        title: "导出成功",
        description: `分类 "${exportName}" 数据导出成功！`,
      })
      setShowExportCategoryDialog(false)
    } catch (error) {
      // Removed setMessage as it's not used in page.tsx for this dialog
      console.error("Export category data error:", error)
      toast({
        title: "导出失败",
        description: `导出分类 "${exportName}" 数据失败，请重试。`,
        variant: "destructive",
      })
    }
  }

  const handleAddToolWrapper = (tool: Omit<Tool, "id" | "addedAt" | "sensitive">) => {
    // This wrapper is for QuickAddDialog, which provides a simplified tool object
    // We need to convert it to the full Tool type before passing to handleAddTool
    const fullTool: Tool = {
      ...tool,
      id: Date.now().toString(), // Generate ID here
      addedAt: new Date().toISOString(),
      sensitive: false, // Default sensitive to false
      subcategory: tool.subcategory || "", // Ensure subcategory is not undefined
      subsubcategory: tool.subsubcategory || "", // Ensure subsubcategory is not undefined
    }
    handleAddTool(fullTool) // Directly call handleAddTool from page.tsx
  }

  // Get category path
  // Optimized with useCallback to avoid unnecessary ToolCard re-renders
  const getCategoryPath = useCallback(
    (categoryId: string, subcategoryId: string, subsubcategoryId: string) => {
      const category = categories.find((c) => c.id === categoryId)
      const categoryName = category?.name || ""

      const subcategory = category?.subcategories?.find((s) => s.id === subcategoryId)
      const subcategoryName = subcategory?.name || ""

      const subsubcategory = subcategory?.subsubcategories?.find((ss) => ss.id === subsubcategoryId)
      const subsubcategoryName = subsubcategory?.name || ""

      // Add warning logs to check for unfound category paths in dev tools
      if (!category) console.warn(`Category not found for ID: ${categoryId}`)
      if (category && !subcategory)
        console.warn(`Subcategory not found for ID: ${subcategoryId} in category ${categoryId}`)
      if (subcategory && !subsubcategory)
        console.warn(`Subsubcategory not found for ID: ${subsubcategoryId} in subcategory ${subcategoryId}`)

      // Return only the parts that exist, joined by " > "
      return [categoryName, subcategoryName, subsubcategoryName].filter(Boolean).join(" > ")
    },
    [categories],
  ) // Depends on categories array, recreate function only when categories change

  // Calculate tool counts for categories
  const getToolCountForCategory = useCallback((categoryId: string, subcategoryId?: string, subsubcategoryId?: string) => {
    return tools.filter(tool => {
      if (subsubcategoryId) {
        return tool.category === categoryId && tool.subcategory === subcategoryId && tool.subsubcategory === subsubcategoryId
      } else if (subcategoryId) {
        return tool.category === categoryId && tool.subcategory === subcategoryId
      } else {
        return tool.category === categoryId
      }
    }).length
  }, [tools])

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setSelectedTag("") // Clear tag filter
    setShowDeepSearchResults(false) // Hide deep search results when doing new search
    setShowGlobalSearchResults(false) // Hide global search results when doing new search
    if (query.trim() && !searchHistory.includes(query.trim())) {
      const newHistory = [query.trim(), ...searchHistory.slice(0, 9)] // Keep recent 10 searches
      setSearchHistory(newHistory)
      setTimeout(() => {
        localStorage.setItem("toolmaster-search-history", JSON.stringify(newHistory))
      }, 0)
    }
  }

  // Handle tag click
  const handleTagClick = (tag: string) => {
    setSelectedTag(tag)
    setSearchQuery("") // Clear search input
    setSelectedCategory("") // Clear category filter
    setSelectedSubcategory("")
    setSelectedSubsubcategory("")
    setCurrentPage(1) // Reset pagination
    setLoadedItems(itemsPerPage) // Reset infinite scroll loaded count
    setShowDeepSearchResults(false) // Hide deep search results
    setShowGlobalSearchResults(false) // Hide global search results
    window.scrollTo({ top: 0, behavior: "smooth" }) // Scroll to top
  }

  // Handle deep search
  const handleDeepSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "搜索内容不能为空",
        description: "请输入您要搜索的内容",
        variant: "destructive",
      })
      return
    }

    setIsDeepSearching(true)
    try {
      // 调用深度搜索API
      const { deepSearch } = await import('@/lib/api-client')
      const { data: result } = await deepSearch(searchQuery, tools)

      // 验证结果数据
      if (!result || typeof result !== 'object') {
        throw new Error('返回的搜索结果格式无效')
      }

      // 确保recommendedTools是数组
      if (!result.recommendedTools || !Array.isArray(result.recommendedTools)) {
        result.recommendedTools = []
      }

      // 调试信息
      console.log('深度搜索结果:', result)
      console.log('推荐的工具ID:', result.recommendedTools.map(r => r.id))
      console.log('本地工具ID:', tools.map(t => t.id).slice(0, 10))

      // 检查ID匹配情况
      const matchedTools = tools.filter(tool =>
        result.recommendedTools.some(rec => rec.id === tool.id)
      )
      console.log('匹配到的工具:', matchedTools.length, matchedTools.map(t => t.name))

      setDeepSearchResults(result)
      setShowDeepSearchResults(true)

      customToast.success("深度搜索完成", `为您找到了 ${result.recommendedTools.length} 个推荐工具`)
    } catch (error) {
      console.error('深度搜索失败:', error)
      customToast.error(
        "深度搜索失败",
        error instanceof Error ? error.message : "AI分析过程中出现错误，请稍后重试"
      )
    } finally {
      setIsDeepSearching(false)
    }
  }

  // Handle global search
  const handleGlobalSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "搜索内容不能为空",
        description: "请输入您要搜索的内容",
        variant: "destructive",
      })
      return
    }

    setIsGlobalSearching(true)
    try {
      // 调用全网搜索API
      const { globalSearch } = await import('@/lib/api-client')
      const { data: result } = await globalSearch(searchQuery)

      // 验证结果数据
      if (!result || typeof result !== 'object') {
        throw new Error('返回的搜索结果格式无效')
      }

      // 确保recommendedTools是数组
      if (!result.recommendedTools || !Array.isArray(result.recommendedTools)) {
        result.recommendedTools = []
      }

      // 调试信息
      console.log('全网搜索结果:', result)
      console.log('推荐的工具数量:', result.recommendedTools?.length || 0)
      console.log('推荐的工具:', result.recommendedTools?.map(t => t.name) || [])
      console.log('搜索理解:', result.searchSummary)
      console.log('搜索洞察:', result.searchInsights)

      // 将结果添加到缓存
      setGlobalSearchCache(result.recommendedTools || [])
      setGlobalSearchResults(result)
      setShowGlobalSearchResults(true)

      // 隐藏深度搜索结果
      setShowDeepSearchResults(false)

      customToast.success("全网搜索完成", `为您找到了 ${result.recommendedTools.length} 个全网推荐工具`)
    } catch (error) {
      console.error('全网搜索失败:', error)
      customToast.error(
        "全网搜索失败",
        error instanceof Error ? error.message : "AI分析过程中出现错误，请稍后重试"
      )
    } finally {
      setIsGlobalSearching(false)
    }
  }

  // Handle add global search tool to local database
  const handleAddGlobalToolToLocal = async (globalTool: Tool & { isGlobalSearch: boolean }) => {
    console.log('开始添加全网搜索工具到本站:', globalTool.name)

    try {
      // 检查是否已存在相同的工具
      const existingTool = tools.find(tool =>
        tool.url === globalTool.url || tool.name === globalTool.name
      )

      if (existingTool) {
        console.log('工具已存在:', existingTool.name)
        customToast.warning(
          "工具已存在",
          `"${globalTool.name}" 已经在您的工具库中了`
        )
        return
      }

      // 准备要添加的工具数据（移除全网搜索特有的字段）
      const toolToAdd = {
        name: globalTool.name,
        url: globalTool.url,
        description: globalTool.description,
        tags: globalTool.tags,
        category: globalTool.category,
        subcategory: globalTool.subcategory,
        subsubcategory: globalTool.subsubcategory,
        sensitive: globalTool.sensitive || false
      }

      console.log('准备添加的工具数据:', toolToAdd)

      // 添加到数据库
      console.log('调用 hybridStorage.addTool...')
      const addedTool = await hybridStorage.addTool(toolToAdd)
      console.log('hybridStorage.addTool 返回结果:', addedTool)

      if (addedTool) {
        console.log('工具添加成功:', addedTool.name, 'ID:', addedTool.id)

        // 使用自定义toast显示成功提示
        console.log('准备显示自定义toast提示')
        customToast.success(
          "添加成功",
          `"${globalTool.name}" 已成功添加到您的工具库`
        )
        console.log('自定义toast调用完成')

        // 不需要从搜索结果中移除工具，保持搜索结果不变
        // 不需要跳转到主页，保持在当前搜索结果页面
        console.log('工具添加完成，保持在当前页面')

      } else {
        console.log('hybridStorage.addTool 返回了空值')
        throw new Error('添加工具失败：hybridStorage.addTool 返回空值')
      }
    } catch (error) {
      console.error('添加全网搜索工具失败:', error)
      customToast.error(
        "添加失败",
        error instanceof Error ? error.message : "添加工具时出现错误"
      )
    }
  }

  // Filter tools - 根据不同的搜索模式显示不同的结果
  const filteredTools = showGlobalSearchResults && globalSearchResults && globalSearchResults.recommendedTools && Array.isArray(globalSearchResults.recommendedTools)
    ? globalSearchResults.recommendedTools.filter(tool => !tool.sensitive || showSensitive)
        .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
    : showDeepSearchResults && deepSearchResults && deepSearchResults.recommendedTools
    ? tools.filter(tool =>
        deepSearchResults.recommendedTools?.some(rec => rec.id === tool.id) &&
        (!tool.sensitive || showSensitive)
      ).sort((a, b) => {
        // 按照AI推荐的相关性评分排序
        const scoreA = deepSearchResults.recommendedTools?.find(rec => rec.id === a.id)?.relevanceScore || 0
        const scoreB = deepSearchResults.recommendedTools?.find(rec => rec.id === b.id)?.relevanceScore || 0
        return scoreB - scoreA
      })
    : tools.filter((tool) => {
        // 原有的搜索匹配逻辑
        let matchesSearch = true
        if (searchQuery && searchQuery.trim()) {
          const query = searchQuery.toLowerCase().trim()
          matchesSearch =
            tool.name.toLowerCase().includes(query) ||
            tool.description.toLowerCase().includes(query) ||
            tool.url.toLowerCase().includes(query) ||
            tool.tags.some((tag) => tag.toLowerCase().includes(query)) ||
            // Add category search
            getCategoryPath(tool.category, tool.subcategory, tool.subsubcategory)
              .toLowerCase()
              .includes(query)
        }

        const matchesCategory = !selectedCategory || tool.category === selectedCategory
        const matchesSubcategory = !selectedSubcategory || tool.subcategory === selectedSubcategory
        const matchesSubsubcategory = !selectedSubsubcategory || tool.subsubcategory === selectedSubsubcategory
        const matchesTag = !selectedTag || tool.tags.includes(selectedTag) // New: tag filter

        const showTool = !tool.sensitive || showSensitive

        return matchesSearch && matchesCategory && matchesSubcategory && matchesSubsubcategory && matchesTag && showTool
      })

  // Pagination handling
  const totalItems = filteredTools.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)

  // Get tools for current page
  const getCurrentPageTools = () => {
    if (viewMode === "infinite") {
      return filteredTools.slice(0, loadedItems)
    } else {
      const startIndex = (currentPage - 1) * itemsPerPage
      const endIndex = startIndex + itemsPerPage
      return filteredTools.slice(startIndex, endIndex)
    }
  }

  const currentTools = getCurrentPageTools()

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  // Handle items per page change
  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items)
    setCurrentPage(1) // Reset to first page
  }

  // Load more for infinite scroll
  const loadMore = () => {
    const newLoadedItems = Math.min(loadedItems + itemsPerPage, totalItems)
    setLoadedItems(newLoadedItems)
  }

  // Reset pagination state (when search or filter changes)
  useEffect(() => {
    setCurrentPage(1)
    setLoadedItems(itemsPerPage)
  }, [searchQuery, selectedCategory, selectedSubcategory, selectedSubsubcategory, selectedTag, itemsPerPage]) // Added selectedTag to dependencies

  // Get current selected category info
  const getCurrentCategory = () => {
    return categories.find((cat) => cat.id === selectedCategory)
  }

  const getCurrentSubcategory = () => {
    const category = getCurrentCategory()
    return category?.subcategories.find((sub) => sub.id === selectedSubcategory)
  }

  // Handle category path click
  const handleCategoryPathClick = (category: string, subcategory: string, subsubCategory: string) => {
    setSelectedCategory(category)
    setSelectedSubcategory(subcategory)
    setSelectedSubsubcategory(subsubCategory)
    setSelectedTag("") // Clear tag filter
    setSidebarOpen(true) // Open sidebar
    setCurrentPage(1) // Reset pagination
    setLoadedItems(itemsPerPage) // Reset infinite scroll loaded count
    window.scrollTo({ top: 0, behavior: "smooth" }) // Scroll to top
  }

  // Handle tool deletion
  const handleDeleteToolInternal = (toolId: string) => {
    const tool = tools.find((t) => t.id === toolId)
    if (tool) {
      setToolToDelete(tool)
      setShowDeleteConfirmDialog(true)
    }
  }

  const handleDeleteToolWrapper = (id: string) => {
    handleDeleteToolInternal(id)
  }

  const confirmDeleteTool = async () => {
    if (toolToDelete) {
      try {
        // 调用数据库删除
        const success = await hybridStorage.deleteTool(toolToDelete.id)

        if (success) {
          // 从本地状态删除
          const updatedTools = tools.filter((t) => t.id !== toolToDelete.id)
          setTools(updatedTools)

          // Log deletion
          const log = {
            action: "DELETE_TOOL",
            tool: toolToDelete.name,
            timestamp: new Date().toISOString(),
          }
          // Defer log write to next event loop to avoid blocking UI
          setTimeout(() => {
            const logs = JSON.parse(localStorage.getItem("toolmaster-logs") || "[]")
            logs.push(log)
            localStorage.setItem("toolmaster-logs", JSON.stringify(logs.slice(-100)))
          }, 0)

          // Use customToast to notify user of successful deletion
          customToast.success("删除成功", `工具 "${toolToDelete.name}" 已删除。`)
        } else {
          customToast.error("删除失败", "删除工具时发生错误")
        }
      } catch (error) {
        console.error('删除工具失败:', error)
        toast({
          title: "删除失败",
          description: "删除工具时发生错误",
          variant: "destructive"
        })
      }

      setToolToDelete(null)
      setShowDeleteConfirmDialog(false)
      // Clear search or category filters to ensure deleted tool is no longer displayed
      setSearchQuery("")
      setSelectedCategory("")
      setSelectedSubcategory("")
      setSelectedSubsubcategory("")
      setSelectedTag("")
      setShowDeepSearchResults(false)
      setShowGlobalSearchResults(false)
    }
  }

  // Handle copying tool info
  const handleCopyToolInfo = (type: "name" | "url" | "description", value: string) => {
    navigator.clipboard
      .writeText(value)
      .then(() => {
        customToast.success(
          "复制成功",
          `${type === "name" ? "名称" : type === "url" ? "链接" : "描述"}已复制到剪贴板。`
        )
      })
      .catch((err) => {
        customToast.error("复制失败", "无法复制到剪贴板，请手动复制。")
        console.error("Failed to copy: ", err)
      })
  }

  // Handle viewing tool description
  const handleViewDescription = (tool: Tool) => {
    setCurrentToolDetails(tool)
    setShowToolDetailsDialog(true)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Toolbox component acts as the main header and manages its own dialogs */}
      <Toolbox
        tools={tools}
        categories={categories}
        onAddTool={handleAddToolWrapper}
        onUpdateTool={handleUpdateTool}
        onDeleteTool={handleDeleteToolWrapper}
        onUpdateCategories={handleUpdateCategories}
        onImportData={handleImportData}
        onExportData={handleExportData}
        getCategoryPath={getCategoryPath}
        searchQuery={searchQuery} // Pass searchQuery
        onSearch={handleSearch} // Pass handleSearch
      />

      <div className="flex">
        {/* Sidebar */}
        <aside
          className={`w-64 border-r bg-muted/10 transition-transform duration-200 ${
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          } md:translate-x-0 fixed md:static h-[calc(100vh-4rem)] z-40`}
        >
          <ScrollArea className="h-full">
            <div className="p-4">
              <h3 className="font-semibold mb-4">分类导航</h3>

              <div className="space-y-2">
                <Button
                  variant={!selectedCategory && !selectedTag ? "secondary" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => {
                    setSelectedCategory("")
                    setSelectedSubcategory("")
                    setSelectedSubsubcategory("")
                    setSelectedTag("")
                    setShowDeepSearchResults(false)
                    setShowGlobalSearchResults(false)
                  }}
                >
                  全部工具 ({tools.length})
                </Button>

                {categories.map((category) => (
                  <div key={category.id} className="space-y-1">
                    <Button
                      variant={selectedCategory === category.id && !selectedTag ? "secondary" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setSelectedCategory(category.id)
                        setSelectedSubcategory("")
                        setSelectedSubsubcategory("")
                        setSelectedTag("")
                      }}
                    >
                      {category.name} ({getToolCountForCategory(category.id)})
                    </Button>

                    {selectedCategory === category.id && (
                      <div className="ml-4 space-y-1">
                        {category.subcategories.length === 0 && (
                          <p className="text-muted-foreground text-sm">暂无子分类。</p>
                        )}
                        {category.subcategories.map((subcategory) => (
                          <div key={subcategory.id} className="space-y-1">
                            <Button
                              variant={selectedSubcategory === subcategory.id && !selectedTag ? "secondary" : "ghost"}
                              size="sm"
                              className="w-full justify-start"
                              onClick={() => {
                                setSelectedSubcategory(subcategory.id)
                                setSelectedSubsubcategory("")
                                setSelectedTag("")
                              }}
                            >
                              {subcategory.name} ({getToolCountForCategory(category.id, subcategory.id)})
                            </Button>

                            {selectedSubcategory === subcategory.id && (
                              <div className="ml-4 space-y-1">
                                {subcategory.subsubcategories.length === 0 && (
                                  <p className="text-muted-foreground text-xs">暂无子子分类。</p>
                                )}
                                {subcategory.subsubcategories.map((subsub) => (
                                  <Button
                                    key={subsub.id}
                                    variant={
                                      selectedSubsubcategory === subsub.id && !selectedTag ? "secondary" : "ghost"
                                    }
                                    size="sm"
                                    className="w-full justify-start text-xs"
                                    onClick={() => {
                                      setSelectedSubsubcategory(subsub.id)
                                      setSelectedTag("")
                                    }}
                                  >
                                    {subsub.name} ({getToolCountForCategory(category.id, subcategory.id, subsub.id)})
                                  </Button>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </ScrollArea>
        </aside>

        {/* Main content area */}
        <main className="flex-1 p-6">
          {/* Mobile search */}
          <div className="sm:hidden mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="搜索工具..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Breadcrumb navigation and tag filter display */}
          {(selectedCategory || selectedSubcategory || selectedSubsubcategory || selectedTag) && (
            <div className="mb-6">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-muted-foreground hover:text-foreground"
                  onClick={() => {
                    setSelectedCategory("")
                    setSelectedSubcategory("")
                    setSelectedSubsubcategory("")
                    setSelectedTag("")
                    setShowDeepSearchResults(false)
                    setShowGlobalSearchResults(false)
                    setSearchQuery("")
                  }}
                >
                  全部工具
                </Button>
                {selectedCategory && (
                  <>
                    <span>/</span>
                    <span>{getCurrentCategory()?.name}</span>
                  </>
                )}
                {selectedSubcategory && (
                  <>
                    <span>/</span>
                    <span>{getCurrentSubcategory()?.name}</span>
                  </>
                )}
                {selectedSubsubcategory && (
                  <>
                    <span>/</span>
                    <span>
                      {getCurrentSubcategory()?.subsubcategories.find((s) => s.id === selectedSubsubcategory)?.name}
                    </span>
                  </>
                )}
                {selectedTag && (
                  <>
                    <span>/</span>
                    <Badge variant="secondary" className="text-xs">
                      标签: {selectedTag}
                      <X key="selectedTag" className="w-3 h-3 ml-1 cursor-pointer" onClick={() => setSelectedTag("")} />
                    </Badge>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Tool statistics and view control */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                {searchQuery || selectedCategory || selectedTag ? (
                  <>
                    找到 <span className="font-medium text-foreground">{totalItems}</span> 个相关工具
                  </>
                ) : (
                  <>
                    共 <span className="font-medium text-foreground">{totalItems}</span> 个工具
                  </>
                )}
                {viewMode === "pagination" && totalPages > 1 && (
                  <>
                    {" "}
                    · 第 {currentPage} 页，共 {totalPages} 页
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Items per page selection */}
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => handleItemsPerPageChange(Number(value))}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="6">6</SelectItem>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                  <SelectItem value="48">48</SelectItem>
                </SelectContent>
              </Select>

              {/* View mode toggle */}
              <div className="flex border rounded-lg">
                <Button
                  variant={viewMode === "pagination" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("pagination")}
                  className="rounded-r-none"
                >
                  分页
                </Button>
                <Button
                  variant={viewMode === "infinite" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("infinite")}
                  className="rounded-l-none"
                >
                  滚动
                </Button>
              </div>
            </div>
          </div>

          {/* Search suggestions */}
          {searchQuery && filteredTools.length === 0 && (
            <SearchSuggestions
              searchQuery={searchQuery}
              tools={tools}
              onSuggestionClick={(suggestion) => handleSearch(suggestion)}
            />
          )}

          {/* Tool list */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {currentTools.map((tool) => {
              try {
                // 获取深度搜索的推荐信息
                const deepRecommendInfo = showDeepSearchResults && deepSearchResults && deepSearchResults.recommendedTools
                  ? deepSearchResults.recommendedTools.find(rec => rec.id === tool.id)
                  : null

                // 检查是否是全网搜索结果
                const isGlobalSearchTool = showGlobalSearchResults && tool.isGlobalSearch
                const globalRecommendInfo = isGlobalSearchTool ? {
                  recommendReason: tool.recommendReason,
                  relevanceScore: tool.relevanceScore
                } : null

                // 检查全网搜索的工具是否已经在本地工具库中
                const isAlreadyInLocal = isGlobalSearchTool ? tools.some(localTool =>
                  localTool.url === tool.url || localTool.name === tool.name
                ) : false

                return (
                  <ToolCard
                    key={tool.id}
                    tool={tool}
                    getCategoryPath={getCategoryPath}
                    onCategoryPathClick={handleCategoryPathClick}
                    onTagClick={handleTagClick}
                    onDeleteTool={handleDeleteToolInternal}
                    onCopyToolInfo={handleCopyToolInfo}
                    onViewDescription={handleViewDescription}
                    recommendReason={deepRecommendInfo?.recommendReason || globalRecommendInfo?.recommendReason}
                    relevanceScore={deepRecommendInfo?.relevanceScore || globalRecommendInfo?.relevanceScore}
                    isGlobalSearchResult={isGlobalSearchTool}
                    onAddToLocal={isGlobalSearchTool && !isAlreadyInLocal ? () => handleAddGlobalToolToLocal(tool as any) : undefined}
                    isAlreadyInLocal={isAlreadyInLocal}
                  />
                )
              } catch (error) {
                console.error("Error rendering ToolCard for tool:", tool.id, error)
                return null
              }
            })}
          </div>

          {/* Pagination controls */}
          {viewMode === "pagination" && totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                上一页
              </Button>

              <div className="flex items-center gap-1">
                {/* Display page numbers */}
                {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                  let pageNum: number
                  if (totalPages <= 7) {
                    pageNum = i + 1
                  } else if (currentPage <= 4) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 3) {
                    pageNum = totalPages - 6 + i
                  } else {
                    pageNum = currentPage - 3 + i
                  }

                  if (pageNum < 1 || pageNum > totalPages) return null

                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      className="w-10"
                    >
                      {pageNum}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>

              {/* Jump to page */}
              <div className="flex items-center gap-2 ml-4">
                <span className="text-sm text-muted-foreground">跳转到</span>
                <Input
                  type="number"
                  min="1"
                  max={totalPages}
                  className="w-16 h-8"
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      const page = Number((e.target as HTMLInputElement).value)
                      if (page >= 1 && page <= totalPages) {
                        handlePageChange(page)
                      }
                    }
                  }}
                />
                <span className="text-sm text-muted-foreground">页</span>
              </div>
            </div>
          )}

          {/* Infinite scroll load more */}
          {viewMode === "infinite" && loadedItems < totalItems && (
            <div className="flex justify-center mt-8">
              <Button onClick={loadMore} variant="outline">
                加载更多 ({totalItems - loadedItems} 个剩余)
              </Button>
            </div>
          )}

          {/* Infinite scroll end message */}
          {viewMode === "infinite" && loadedItems >= totalItems && totalItems > itemsPerPage && (
            <div className="text-center mt-8 text-sm text-muted-foreground">已显示全部 {totalItems} 个工具</div>
          )}

          {currentTools.length === 0 && !showDeepSearchResults && !showGlobalSearchResults && (
            <div className="text-center py-12">
              <div className="text-muted-foreground mb-6">
                {searchQuery || selectedTag ? `未找到包含 "${searchQuery || selectedTag}" 的工具` : "暂无工具"}
              </div>
              {(searchQuery || selectedTag) && (
                <div className="space-y-6">
                  {/* 搜索操作按钮区域 */}
                  <div className="flex flex-col items-center space-y-4">
                    {/* 深度搜索和全网搜索按钮 - 只在有搜索查询时显示 */}
                    {searchQuery && !selectedTag && (
                      <div className="flex flex-col sm:flex-row gap-3 w-full max-w-md">
                        <Button
                          variant="outline"
                          onClick={handleDeepSearch}
                          disabled={isDeepSearching || isGlobalSearching}
                          className="flex-1 min-w-0 border-blue-500/50 text-blue-600 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-500 dark:border-blue-400/50 dark:text-blue-400 dark:hover:bg-blue-950/20 dark:hover:border-blue-400 transition-all duration-200"
                        >
                          {isDeepSearching ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              <span className="truncate">深度搜索中...</span>
                            </>
                          ) : (
                            <>
                              <Search className="mr-2 h-4 w-4 flex-shrink-0" />
                              <span className="truncate">深度搜索</span>
                            </>
                          )}
                        </Button>

                        <Button
                          variant="outline"
                          onClick={handleGlobalSearch}
                          disabled={isGlobalSearching || isDeepSearching}
                          className="flex-1 min-w-0 border-green-500/50 text-green-600 hover:bg-green-50 hover:text-green-700 hover:border-green-500 dark:border-green-400/50 dark:text-green-400 dark:hover:bg-green-950/20 dark:hover:border-green-400 transition-all duration-200"
                        >
                          {isGlobalSearching ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              <span className="truncate">全网搜索中...</span>
                            </>
                          ) : (
                            <>
                              <Search className="mr-2 h-4 w-4 flex-shrink-0" />
                              <span className="truncate">全网搜索</span>
                            </>
                          )}
                        </Button>
                      </div>
                    )}

                    {/* 清除筛选按钮 */}
                    <Button
                      variant="ghost"
                      onClick={() => {
                        setSearchQuery("")
                        setSelectedTag("")
                        setShowDeepSearchResults(false)
                        setShowGlobalSearchResults(false)
                      }}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <X className="mr-2 h-4 w-4" />
                      清除筛选
                    </Button>
                  </div>

                  {/* 提示文本 */}
                  <div className="text-sm text-muted-foreground max-w-lg mx-auto">
                    {searchQuery && !selectedTag ? (
                      <div className="space-y-2">
                        <p>试试上方的搜索功能：</p>
                        <div className="flex flex-wrap justify-center gap-2 text-xs">
                          <span className="px-2 py-1 bg-blue-50 text-blue-600 rounded-md dark:bg-blue-950/20 dark:text-blue-400">
                            深度搜索：本站内智能推荐
                          </span>
                          <span className="px-2 py-1 bg-green-50 text-green-600 rounded-md dark:bg-green-950/20 dark:text-green-400">
                            全网搜索：获取全网最佳工具
                          </span>
                        </div>
                      </div>
                    ) : (
                      <p>尝试搜索：工具名称、描述、标签、分类或URL</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 深度搜索结果显示 */}
          {showDeepSearchResults && deepSearchResults && (
            <div className="mb-8">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg p-6 mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-foreground">深度搜索结果</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>搜索理解：</strong>{deepSearchResults.searchSummary}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>分析洞察：</strong>{deepSearchResults.searchInsights}
                </p>
                <div className="mt-4 flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowDeepSearchResults(false)
                      setDeepSearchResults(null)
                    }}
                  >
                    返回常规搜索
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSearchQuery("")
                      setShowDeepSearchResults(false)
                      setDeepSearchResults(null)
                    }}
                  >
                    清除搜索
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* 全网搜索结果显示 */}
          {showGlobalSearchResults && globalSearchResults && (
            <div className="mb-8">
              <div className="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-950/20 dark:to-teal-950/20 rounded-lg p-6 mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-teal-600 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-foreground">全网搜索结果</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>搜索理解：</strong>{globalSearchResults.searchSummary}
                </p>
                <p className="text-sm text-muted-foreground">
                  <strong>搜索洞察：</strong>{globalSearchResults.searchInsights}
                </p>
                <div className="mt-4 flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowGlobalSearchResults(false)
                      setGlobalSearchResults(null)
                    }}
                  >
                    返回常规搜索
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSearchQuery("")
                      setShowGlobalSearchResults(false)
                      setGlobalSearchResults(null)
                    }}
                  >
                    清除搜索
                  </Button>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* Mobile overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 bg-black/20 z-30 md:hidden" onClick={() => setSidebarOpen(false)} />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirmDialog} onOpenChange={setShowDeleteConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除工具？</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除工具 "{toolToDelete?.name}" 吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteTool} className="bg-red-600 hover:bg-red-700">
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Tool Details Dialog */}
      <Dialog open={showToolDetailsDialog} onOpenChange={setShowToolDetailsDialog}>
        <DialogContent className="sm:max-w-lg max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{currentToolDetails?.name} 的详细描述</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">{currentToolDetails?.description || "暂无详细描述。"}</p>
            {currentToolDetails?.url && (
              <div className="text-sm text-muted-foreground">
                <span className="font-medium">链接: </span>
                <a
                  href={currentToolDetails.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  {currentToolDetails.url}
                </a>
              </div>
            )}
            {currentToolDetails?.tags && currentToolDetails.tags.length > 0 && (
              <div>
                <span className="text-sm font-medium text-muted-foreground">标签: </span>
                <div className="flex flex-wrap gap-1 mt-1">
                  <Badge key={currentToolDetails.category} variant="outline" className="text-xs">
                    {currentToolDetails.category}
                  </Badge>
                  {currentToolDetails.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            {currentToolDetails?.category && (
              <div className="text-sm text-muted-foreground">
                <span className="font-medium">分类: </span>
                {getCategoryPath(
                  currentToolDetails.category,
                  currentToolDetails.subcategory,
                  currentToolDetails.subsubcategory,
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Category Data Dialog */}
      <Dialog open={showExportCategoryDialog} onOpenChange={setShowExportCategoryDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>导出分类数据</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              您即将导出分类 <strong>{categoryToExport?.name}</strong> 下的所有工具数据。
            </p>
            <Button onClick={confirmExportCategoryData} className="w-full">
              确认导出
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
